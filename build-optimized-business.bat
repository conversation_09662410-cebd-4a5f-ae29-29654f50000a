@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo ============================================
echo RPA-K8s Optimized Business Images Builder
echo ============================================
echo.

:: Registry configuration
set REGISTRY_PREFIX=crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai
set USERNAME=lingyichuhai
set PASSWORD=lingyichuhai2025

:: Image names
set BASE_IMAGE=%REGISTRY_PREFIX%/rpa-k8s-base:latest
set SHOP_ACCOUNT_IMAGE=%REGISTRY_PREFIX%/rpa-k8s-shop-account-info:latest
set SYN_PUBLISH_IMAGE=%REGISTRY_PREFIX%/rpa-k8s-syn-publish-list:latest
set BLOCK_UNBLOCK_IMAGE=%REGISTRY_PREFIX%/rpa-k8s-block-unblock:latest

:: Check if base image exists locally
echo [Check] Checking base image availability...
docker image inspect %BASE_IMAGE% >nul 2>&1
if !errorlevel! neq 0 (
    echo [Info] Base image not found locally, pulling from registry...
    docker pull %BASE_IMAGE%
    if !errorlevel! neq 0 (
        echo [Error] Failed to pull base image
        exit /b 1
    )
) else (
    echo [Info] Base image found locally
)

:: Verify aiofiles dependency
echo [Check] Verifying aiofiles dependency in base image...
docker run --rm %BASE_IMAGE% python -c "import aiofiles; print('aiofiles OK')" >nul 2>&1
if !errorlevel! neq 0 (
    echo [Error] aiofiles dependency not found in base image
    exit /b 1
) else (
    echo [Info] aiofiles dependency verified
)

:: Login to registry
echo [Auth] Logging into registry...
echo %PASSWORD% | docker login %REGISTRY_PREFIX% -u %USERNAME% --password-stdin
if !errorlevel! neq 0 (
    echo [Error] Registry login failed
    exit /b 1
)

:: Build function
goto MAIN

:BUILD_IMAGE
set MODULE_NAME=%~1
set IMAGE_NAME=%~2
set DOCKERFILE_PATH=%~3

echo.
echo ============================================
echo [Build] Building %MODULE_NAME%
echo ============================================
echo [Build] Image: %IMAGE_NAME%
echo [Build] Dockerfile: %DOCKERFILE_PATH%

set START_TIME=%TIME%

:: Build with cache optimization
docker build --cache-from %IMAGE_NAME% -f "%DOCKERFILE_PATH%" -t "%IMAGE_NAME%" .

if !errorlevel! neq 0 (
    echo [Error] %MODULE_NAME% build failed
    exit /b 1
)

set END_TIME=%TIME%
echo [Success] %MODULE_NAME% build completed

:: Push with retry mechanism
echo [Push] Pushing %IMAGE_NAME%...
set RETRY_COUNT=0
:PUSH_RETRY
docker push "%IMAGE_NAME%"
if !errorlevel! equ 0 (
    echo [Success] %MODULE_NAME% pushed successfully
    goto :EOF
) else (
    set /a RETRY_COUNT+=1
    if !RETRY_COUNT! lss 3 (
        echo [Retry] Push failed, retrying (!RETRY_COUNT!/3)...
        timeout /t 10 /nobreak >nul
        goto PUSH_RETRY
    ) else (
        echo [Error] %MODULE_NAME% push failed after 3 retries
        exit /b 1
    )
)

:MAIN
:: Parse command line arguments
set BUILD_TYPE=%1
if "%BUILD_TYPE%"=="" set BUILD_TYPE=all

if "%BUILD_TYPE%"=="shop" goto BUILD_SHOP
if "%BUILD_TYPE%"=="syn" goto BUILD_SYN
if "%BUILD_TYPE%"=="block" goto BUILD_BLOCK
if "%BUILD_TYPE%"=="all" goto BUILD_ALL
if "%BUILD_TYPE%"=="business" goto BUILD_ALL

echo [Error] Invalid build type. Use: shop, syn, block, all, or business
exit /b 1

:BUILD_SHOP
call :BUILD_IMAGE "shop_account_info" "%SHOP_ACCOUNT_IMAGE%" "app/business/shop_account_info/Dockerfile"
goto SUCCESS

:BUILD_SYN
call :BUILD_IMAGE "syn_publish_list" "%SYN_PUBLISH_IMAGE%" "app/business/export_publish_list/Dockerfile"
goto SUCCESS

:BUILD_BLOCK
call :BUILD_IMAGE "block_unblock" "%BLOCK_UNBLOCK_IMAGE%" "app/business/block_unblock_management/Dockerfile"
goto SUCCESS

:BUILD_ALL
call :BUILD_IMAGE "shop_account_info" "%SHOP_ACCOUNT_IMAGE%" "app/business/shop_account_info/Dockerfile"
call :BUILD_IMAGE "syn_publish_list" "%SYN_PUBLISH_IMAGE%" "app/business/export_publish_list/Dockerfile"
call :BUILD_IMAGE "block_unblock" "%BLOCK_UNBLOCK_IMAGE%" "app/business/block_unblock_management/Dockerfile"
goto SUCCESS

:SUCCESS
echo.
echo ============================================
echo [Complete] All builds completed successfully
echo ============================================
echo.
echo [Images] Available images:
echo 1. Base: %BASE_IMAGE%
echo 2. Shop Account: %SHOP_ACCOUNT_IMAGE%
echo 3. Syn Publish: %SYN_PUBLISH_IMAGE%
echo 4. Block Unblock: %BLOCK_UNBLOCK_IMAGE%
echo.
echo [Usage] Examples:
echo   build-optimized-business.bat shop    # Build shop account only
echo   build-optimized-business.bat syn     # Build syn publish only
echo   build-optimized-business.bat block   # Build block unblock only
echo   build-optimized-business.bat all     # Build all business images
echo.
pause
