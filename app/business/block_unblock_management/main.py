"""
屏蔽与解屏蔽管理主程序

功能：
- 自动化屏蔽与解屏蔽SKU的完整流程
- 亿迈RPA登录 + 公告通知接口调用
- Excel文件下载与多sheet数据处理
- 数据库查询匹配与Excel模板生成
- 批量库存更新接口调用
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.core.base_rpa_async import AsyncBaseRPA
from app.business.block_unblock_management.block_manager import BlockManager
from app.business.block_unblock_management.config import BlockUnblockConfig


class BlockUnblockRPA(AsyncBaseRPA):
    """
    屏蔽解屏蔽管理RPA
    
    自动化屏蔽与解屏蔽SKU的完整流程：
    1. 亿迈系统登录和认证信息提取
    2. 公告通知列表接口调用，获取昨日屏蔽文件
    3. Excel文件下载和多sheet数据解析
    4. 数据库查询匹配publish_success_list表
    5. Excel导入模板生成（店铺别称、SellerSKU、在线库存）
    6. 批量改库存接口调用
    """
    
    def __init__(self):
        """初始化屏蔽解屏蔽RPA"""
        super().__init__(
            business_type="block_unblock_management",
            script_name="block_unblock_management"
        )
        
        # 初始化屏蔽管理器
        self.block_manager = BlockManager(
            logger=self.logger,
            business_type=self.business_type,
            script_name=self.script_name
        )
        
        # 加载配置
        self.config = BlockUnblockConfig.get_default_config()
        
        # 显示环境信息
        from app.config.config import ConfigManager
        ConfigManager.print_environment_summary(self.business_type, self.script_name)
        
        self.logger.info("屏蔽解屏蔽RPA初始化完成")
    
    async def _test_database_integration(self) -> dict:
        """
        测试数据库集成功能
        
        Returns:
            dict: 测试结果
        """
        try:
            self.logger.info("开始数据库集成测试")
            
            # 调用屏蔽管理器的数据库集成测试
            test_result = await self.block_manager.test_database_integration()
            
            return {
                'success': test_result.get('success', False),
                'message': test_result.get('message', '数据库集成测试完成'),
                'test_result': test_result
            }
            
        except Exception as e:
            self.logger.error(f"数据库集成测试异常: {str(e)}")
            return {
                'success': False,
                'message': f'数据库集成测试异常: {str(e)}',
                'error': str(e)
            }
    
    async def _show_database_statistics(self) -> dict:
        """
        显示数据库统计信息
        
        Returns:
            dict: 统计信息
        """
        try:
            self.logger.info("获取数据库统计信息")
            
            # 获取数据库统计
            from app.business.export_publish_list.db_operations import PublishSuccessDBOperator
            
            db_operator = PublishSuccessDBOperator(
                business_type=self.business_type,
                script_name=self.script_name,
                logger=self.logger
            )
            
            # 基础统计查询
            stats_sql = """
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT sku) as unique_skus,
                    COUNT(DISTINCT account) as unique_accounts,
                    COUNT(DISTINCT platform) as unique_platforms,
                    MIN(create_time) as earliest_create_time,
                    MAX(create_time) as latest_create_time
                FROM yimai_publish_success_list
            """
            
            basic_stats = db_operator.db_manager.execute_query(stats_sql)
            
            return {
                'success': True,
                'message': '数据库统计信息获取成功',
                'statistics': basic_stats[0] if basic_stats else {},
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取数据库统计信息异常: {str(e)}")
            return {
                'success': False,
                'message': f'获取数据库统计信息异常: {str(e)}',
                'error': str(e)
            }
    
    async def _do_execute(self) -> dict:
        """
        执行屏蔽解屏蔽任务的核心逻辑
        
        Returns:
            dict: 执行结果
        """
        try:
            # 检查命令行参数以确定执行模式
            args = sys.argv[1:] if len(sys.argv) > 1 else []
            
            if 'db_test' in args:
                # 数据库测试模式
                self.logger.info("进入数据库测试模式")
                return await self._test_database_integration()
            elif 'db_stats' in args:
                # 数据库统计模式
                self.logger.info("进入数据库统计模式")
                return await self._show_database_statistics()
            
            # 正常执行模式
            self.logger.info("开始执行屏蔽解屏蔽任务")
            
            # 设置查询时间范围（默认昨天）
            date_range = BlockUnblockConfig.get_yesterday_date_range()
            self.logger.info(f"查询时间范围: {date_range}")
            
            # 使用浏览器上下文执行完整流程
            async with self.web_driver_context() as driver:
                # 执行完整的屏蔽解屏蔽流程
                result = await self.block_manager.execute_block_unblock_flow(
                    driver=driver,
                    date_range=date_range
                )
            
            if result['success']:
                # 确保日志记录的数据结构中不包含set类型和bytes类型
                def convert_sets_to_lists(data):
                    if isinstance(data, set):
                        return list(data)
                    elif isinstance(data, bytes):
                        # 处理bytes类型，避免JSON序列化错误
                        try:
                            return data.decode('utf-8')
                        except UnicodeDecodeError:
                            # 如果是二进制数据，只返回长度信息
                            return f"<bytes: {len(data)} bytes>"
                    elif isinstance(data, list):
                        return [convert_sets_to_lists(item) for item in data]
                    elif isinstance(data, dict):
                        return {k: convert_sets_to_lists(v) for k, v in data.items()}
                    elif isinstance(data, tuple):
                        return tuple(convert_sets_to_lists(item) for item in data)
                    else:
                        return data
                
                safe_result = convert_sets_to_lists(result)
                self.logger.info("屏蔽解屏蔽任务执行成功", extra_data=safe_result)
                
                # 🔧 清理临时文件（仅清理业务临时文件）
                await self._cleanup_temp_files()
                
                return {
                    'success': True,
                    'message': result.get('message', '屏蔽解屏蔽任务完成'),
                    'processed_data': result.get('processed_data', {}),
                    'template_info': result.get('template_info', {}),
                    'stock_update_result': result.get('stock_update_result', {}),
                    'statistics': result.get('statistics', {})
                }
            else:
                error_msg = result.get('message') or result.get('error', '未知错误')
                self.logger.error(f"屏蔽解屏蔽任务执行失败: {error_msg}")
                
                # 🔧 即使失败也要清理临时文件
                await self._cleanup_temp_files()
                
                return {
                    'success': False,
                    'message': error_msg,
                    'error': result.get('error', error_msg)
                }
                
        except Exception as e:
            error_str = str(e)
            self.logger.error(f"屏蔽解屏蔽任务异常: {error_str}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': error_str
            })
            
            # 🔧 发生异常时也要清理临时文件
            await self._cleanup_temp_files()
            
            return {
                'success': False,
                'message': f'屏蔽解屏蔽任务异常: {error_str}',
                'error': error_str
            }
    
    async def _cleanup_temp_files(self):
        """
        清理临时文件（仅清理业务流程产生的临时文件）
        
        屏蔽解屏蔽任务中Excel文件都在内存中处理，
        此方法只清理可能产生的临时目录，不清理项目文件。
        """
        try:
            import tempfile
            from pathlib import Path
            
            cleaned_items = []
            
            # 1. 清理可能的浏览器临时缓存目录
            temp_dirs_to_check = [
                # 检查是否有相关的临时目录
                Path(tempfile.gettempdir()) / "playwright_cache_block_unblock",
                Path(tempfile.gettempdir()) / "block_unblock_temp",
                # 如果web_driver_context创建了临时目录
                Path.cwd() / "temp" / "block_unblock",
            ]
            
            for temp_dir in temp_dirs_to_check:
                if temp_dir.exists() and temp_dir.is_dir():
                    try:
                        import shutil
                        shutil.rmtree(temp_dir)
                        cleaned_items.append(f"临时目录: {temp_dir}")
                        self.logger.debug(f"已清理临时目录: {temp_dir}")
                    except Exception as e:
                        self.logger.debug(f"清理临时目录失败: {temp_dir}, 错误: {str(e)}")
            
            # 2. 清理可能的临时Excel文件（虽然理论上不应该有）
            temp_file_patterns = [
                "block_unblock_*.xlsx",
                "block_unblock_*.tmp"
            ]
            
            temp_dir = Path(tempfile.gettempdir())
            for pattern in temp_file_patterns:
                for temp_file in temp_dir.glob(pattern):
                    if temp_file.is_file():
                        try:
                            temp_file.unlink()
                            cleaned_items.append(f"临时文件: {temp_file.name}")
                            self.logger.debug(f"已清理临时文件: {temp_file}")
                        except Exception as e:
                            self.logger.debug(f"清理临时文件失败: {temp_file}, 错误: {str(e)}")
            
            if cleaned_items:
                # 确保日志记录的数据结构中不包含set类型
                def convert_sets_to_lists(data):
                    if isinstance(data, set):
                        return list(data)
                    elif isinstance(data, bytes):
                        # 处理bytes类型，避免JSON序列化错误
                        try:
                            return data.decode('utf-8')
                        except UnicodeDecodeError:
                            # 如果是二进制数据，只返回长度信息
                            return f"<bytes: {len(data)} bytes>"
                    elif isinstance(data, list):
                        return [convert_sets_to_lists(item) for item in data]
                    elif isinstance(data, dict):
                        return {k: convert_sets_to_lists(v) for k, v in data.items()}
                    elif isinstance(data, tuple):
                        return tuple(convert_sets_to_lists(item) for item in data)
                    else:
                        return data
                
                safe_extra_data = convert_sets_to_lists({
                    'cleaned_items': cleaned_items,
                    'total_cleaned': len(cleaned_items)
                })
                self.logger.info(f"任务完成，已清理 {len(cleaned_items)} 个临时项目", extra_data=safe_extra_data)
            else:
                self.logger.debug("没有找到需要清理的临时文件（Excel处理都在内存中）")
                
        except Exception as e:
            self.logger.warning(f"清理临时文件时发生异常: {str(e)}")


async def main():
    """
    主函数 - 执行屏蔽解屏蔽任务
    """
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("启动屏蔽解屏蔽功能")
    
    try:
        # 创建屏蔽解屏蔽RPA实例并执行
        block_rpa = BlockUnblockRPA()
        result = await block_rpa.execute()
        
        # 处理执行结果
        if result.get('status') == 'success':
            # AsyncBaseRPA.execute()返回的格式
            data = result.get('data', {})
            logger.info("任务执行成功")
            
            # 检查是否是测试模式
            if data.get('test_result'):
                # 数据库测试模式结果
                test_result = data['test_result']
                print(f"\n✅ 数据库集成测试完成")
                print(f"📄 消息: {data.get('message', '测试完成')}")
                
                if test_result.get('connection_result'):
                    conn_result = test_result['connection_result']
                    print(f"\n🔌 数据库连接测试:")
                    print(f"   状态: {'✅ 连接正常' if conn_result.get('connected') else '❌ 连接失败'}")
                    print(f"   消息: {conn_result.get('message', 'N/A')}")
                
                if test_result.get('query_test'):
                    query_test = test_result['query_test']
                    print(f"\n🔍 查询功能测试:")
                    print(f"   测试SKU数量: {len(query_test.get('test_skus', []))}")
                    print(f"   找到映射数量: {query_test.get('mapping_found', 0)}")
                
                return True
                
            elif data.get('statistics') and not data.get('processed_data'):
                # 数据库统计模式结果
                stats = data.get('statistics', {})
                print(f"\n📊 数据库统计信息")
                print(f"📄 消息: {data.get('message', '统计完成')}")
                
                print(f"\n📈 基础统计:")
                print(f"   📝 总记录数: {stats.get('total_records', 0):,}")
                print(f"   🏷️ 唯一SKU数: {stats.get('unique_skus', 0):,}")
                print(f"   🏪 唯一账号数: {stats.get('unique_accounts', 0):,}")
                print(f"   🌐 唯一平台数: {stats.get('unique_platforms', 0):,}")
                
                if stats.get('earliest_create_time') and stats.get('latest_create_time'):
                    print(f"   📅 数据时间范围: {stats['earliest_create_time']} ~ {stats['latest_create_time']}")
                
                return True
            else:
                # 正常业务流程结果
                print(f"\n✅ 屏蔽解屏蔽任务执行成功")
                print(f"📄 消息: {data.get('message', '任务完成')}")
                
                # 显示处理结果
                if data.get('processed_data'):
                    processed_data = data['processed_data']
                    print(f"\n📊 数据处理结果:")
                    print(f"   🔒 屏蔽SKU数量: {len(processed_data.get('block_skus', []))}")
                    print(f"   🔓 解屏蔽SKU数量: {len(processed_data.get('unblock_skus', []))}")
                
                # 显示模板信息
                if data.get('template_info'):
                    template_info = data['template_info']
                    print(f"\n📋 Excel模板信息:")
                    print(f"   📝 模板行数: {template_info.get('total_rows', 0)}")
                    print(f"   💾 模板大小: {template_info.get('file_size', 0)} 字节")
                
                # 显示库存更新结果
                if data.get('stock_update_result'):
                    stock_result = data['stock_update_result']
                    if stock_result.get('success'):
                        print(f"\n🔄 库存更新结果:")
                        print(f"   ✅ 更新成功数量: {stock_result.get('success_count', 0)}")
                        print(f"   ❌ 更新失败数量: {stock_result.get('failed_count', 0)}")
                    else:
                        print(f"\n❌ 库存更新失败: {stock_result.get('message', '未知错误')}")
                
                # 显示统计信息
                if data.get('statistics'):
                    stats = data['statistics']
                    print(f"\n📈 任务统计:")
                    print(f"   ⏱️ 执行时间: {stats.get('execution_time', 'N/A')}")
                    print(f"   📊 处理文件数: {stats.get('processed_files', 0)}")
                
                return True
            
        else:
            # 执行失败
            logger.error("任务执行失败")
            print(f"\n❌ 屏蔽解屏蔽任务执行失败")
            
            if result.get('status') == 'failed' and 'error' in result:
                error_info = result['error']
                print(f"📄 错误类型: {error_info.get('error_type', 'Unknown')}")
                print(f"📄 错误消息: {error_info.get('error_message', 'Unknown error')}")
            else:
                print(f"📄 消息: {result.get('message', '未知错误')}")
            
            return False
    
    except Exception as e:
        logger.error(f"主函数异常: {e}")
        print(f"\n💥 程序异常: {str(e)}")
        return False


if __name__ == "__main__":
    """
    屏蔽与解屏蔽管理工具
    
    功能：
    - 自动获取昨日屏蔽SKU明细文件
    - 解析Excel多sheet数据并提取SKU
    - 查询数据库匹配对应店铺信息
    - 生成库存调整模板并批量更新
    
    使用方法:
       python main.py              # 正常执行屏蔽解屏蔽流程
       python main.py db_test       # 测试数据库集成功能
       python main.py db_stats      # 显示数据库统计信息
    
    特性：
    - 完整RPA流程（登录 + 接口调用）
    - 智能Excel数据处理和筛选
    - 数据库自动查询匹配
    - 批量库存更新操作
    - 数据库集成测试和统计
    """
    
    # 运行异步主函数
    success = asyncio.run(main())
    
    # 设置退出代码
    sys.exit(0 if success else 1) 