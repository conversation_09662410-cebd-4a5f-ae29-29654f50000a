"""
数据库操作管理器

提供统一的数据库操作接口：
- MySQL连接池管理
- 事务处理和回滚
- SQL执行和结果处理
- 连接健康检查
- 业务级配置支持
"""

import time
import pymysql
from typing import Optional, Dict, List, Any, Tuple, Union
from contextlib import contextmanager
from threading import Lock
import threading
import queue

from app.config.config import ConfigManager
from app.utils.logger import get_rpa_logger


class DatabaseConnectionPool:
    """数据库连接池管理器"""
    
    def __init__(self, config: Dict[str, str], pool_size: int = 10, max_overflow: int = 20):
        """
        初始化数据库连接池
        
        Args:
            config: 数据库配置字典
            pool_size: 连接池基础大小
            max_overflow: 最大溢出连接数
        """
        self.config = config
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        
        # 连接池队列和锁
        self._pool = queue.Queue(maxsize=pool_size + max_overflow)
        self._pool_lock = Lock()
        self._current_connections = 0
        self._max_connections = pool_size + max_overflow
        
        # 预创建基础连接
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池，预创建基础连接数量"""
        for _ in range(self.pool_size):
            try:
                conn = self._create_connection()
                self._pool.put(conn, block=False)
                self._current_connections += 1
            except Exception:
                # 初始化时连接失败，留待后续按需创建
                break
    
    def _create_connection(self) -> pymysql.Connection:
        """
        创建新的数据库连接

        Returns:
            PyMySQL连接对象

        Raises:
            pymysql.Error: 数据库连接失败
        """
        return pymysql.connect(
            host=self.config['host'],
            port=int(self.config['port']),
            user=self.config['username'],
            password=self.config['password'],
            database=self.config['database'],
            charset='utf8mb4',              # 支持emoji和特殊字符
            autocommit=False,               # 默认不自动提交
            connect_timeout=60,             # 连接超时60秒（增加）
            read_timeout=1800,              # 读取超时30分钟（大幅增加）
            write_timeout=1800,             # 写入超时30分钟（大幅增加）
            cursorclass=pymysql.cursors.DictCursor,  # 使用字典游标
            auth_plugin_map={
                'caching_sha2_password': 'mysql_native_password'
            },  # 解决MySQL 8.0认证问题
        )
    
    def get_connection(self) -> pymysql.Connection:
        """
        从连接池获取连接
        
        Returns:
            数据库连接对象
            
        Raises:
            RuntimeError: 无法获取数据库连接
        """
        with self._pool_lock:
            try:
                # 尝试从池中获取连接
                conn = self._pool.get(block=False)
                
                # 检查连接是否还有效
                if self._is_connection_alive(conn):
                    return conn
                else:
                    # 连接已断开，创建新连接
                    self._current_connections -= 1
                    conn = self._create_connection()
                    self._current_connections += 1
                    return conn
                    
            except queue.Empty:
                # 池中没有可用连接，尝试创建新连接
                if self._current_connections < self._max_connections:
                    conn = self._create_connection()
                    self._current_connections += 1
                    return conn
                else:
                    raise RuntimeError("数据库连接池已满，无法创建新连接")
    
    def return_connection(self, conn: pymysql.Connection):
        """
        将连接返回到连接池
        
        Args:
            conn: 要返回的数据库连接
        """
        if conn and self._is_connection_alive(conn):
            try:
                # 重置连接状态
                conn.rollback()  # 回滚未提交的事务
                self._pool.put(conn, block=False)
            except queue.Full:
                # 池已满，直接关闭连接
                conn.close()
                with self._pool_lock:
                    self._current_connections -= 1
        else:
            # 连接已断开
            if conn:
                conn.close()
            with self._pool_lock:
                self._current_connections -= 1
    
    def _is_connection_alive(self, conn: pymysql.Connection) -> bool:
        """
        检查数据库连接是否还活着
        
        Args:
            conn: 数据库连接对象
            
        Returns:
            连接是否有效
        """
        try:
            conn.ping(reconnect=False)
            return True
        except:
            return False
    
    def close_all(self):
        """关闭所有连接"""
        while not self._pool.empty():
            try:
                conn = self._pool.get(block=False)
                conn.close()
            except queue.Empty:
                break
        
        with self._pool_lock:
            self._current_connections = 0


class DatabaseManager:
    """数据库操作管理器 - 提供统一的数据库操作接口"""
    
    # 类级别的连接池缓存
    _connection_pools: Dict[str, DatabaseConnectionPool] = {}
    _pool_lock = Lock()
    
    def __init__(self, business_type: str = None, script_name: str = None, task_id: str = None):
        """
        初始化数据库管理器
        
        Args:
            business_type: 业务类型，用于获取业务级配置
            script_name: 脚本名称，用于日志记录
            task_id: 任务ID，用于追踪和日志关联
        """
        self.business_type = business_type
        self.script_name = script_name
        self.task_id = task_id
        
        # 获取数据库配置（支持业务级配置覆盖）
        self.config = ConfigManager.get_database_config(business_type, script_name)
        
        # 初始化日志记录器
        self.logger = get_rpa_logger(business_type or 'database', script_name or 'db', task_id)
        
        # 生成连接池键（基于配置内容）
        self.pool_key = f"{self.config['host']}:{self.config['port']}/{self.config['database']}"
        
        # 获取或创建连接池
        self._ensure_connection_pool()
        
        self.logger.info("数据库管理器初始化完成", step="db_init", extra_data={
            "host": self.config['host'],
            "database": self.config['database'],
            "pool_key": self.pool_key
        })
    
    def _ensure_connection_pool(self):
        """确保连接池存在"""
        with self._pool_lock:
            if self.pool_key not in self._connection_pools:
                # 创建新的连接池
                pool_size = int(ConfigManager.get_config('DB_POOL_SIZE', '10', self.business_type, self.script_name))
                max_overflow = int(ConfigManager.get_config('DB_MAX_OVERFLOW', '20', self.business_type, self.script_name))
                
                self._connection_pools[self.pool_key] = DatabaseConnectionPool(
                    self.config, pool_size, max_overflow
                )
                self.logger.info(f"创建数据库连接池: {self.pool_key}", step="create_pool")
    
    @property
    def connection_pool(self) -> DatabaseConnectionPool:
        """获取当前的连接池"""
        return self._connection_pools[self.pool_key]
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        Usage:
            with db_manager.get_connection() as conn:
                # 使用连接进行数据库操作
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM table")
        
        Yields:
            数据库连接对象
        """
        conn = None
        try:
            # 从连接池获取连接
            conn = self.connection_pool.get_connection()
            self.logger.info("获取数据库连接", step="get_connection")
            yield conn
            
        except Exception as e:
            self.logger.error(f"数据库连接操作失败: {str(e)}", step="connection_error", exception=e)
            raise
            
        finally:
            # 将连接返回到池中
            if conn:
                self.connection_pool.return_connection(conn)
                self.logger.info("数据库连接已返回池中", step="return_connection")
    
    def execute_query(self, sql: str, params: Tuple = None) -> List[Dict[str, Any]]:
        """
        执行查询SQL语句
        
        Args:
            sql: SQL查询语句
            params: SQL参数元组
            
        Returns:
            查询结果列表，每行为一个字典
            
        Raises:
            pymysql.Error: 数据库操作失败
        """
        start_time = time.time()
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 执行查询
                    affected_rows = cursor.execute(sql, params)
                    results = cursor.fetchall()
                    
                    execution_time = time.time() - start_time
                    
                    # 记录操作日志
                    self.logger.log_database_operation(
                        operation="select",
                        affected_rows=affected_rows,
                        query=sql
                    )
                    
                    self.logger.info(f"查询执行完成，返回 {len(results)} 行，耗时 {execution_time:.3f}秒", 
                                   step="query_complete",
                                   extra_data={"affected_rows": affected_rows, "execution_time": execution_time})
                    
                    return results
                    
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"查询执行失败: {sql}", step="query_error", exception=e,
                            extra_data={"execution_time": execution_time})
            raise
    
    def execute_update(self, sql: str, params: Tuple = None, auto_commit: bool = True) -> int:
        """
        执行更新SQL语句（INSERT, UPDATE, DELETE）
        
        Args:
            sql: SQL更新语句
            params: SQL参数元组
            auto_commit: 是否自动提交事务
            
        Returns:
            受影响的行数
            
        Raises:
            pymysql.Error: 数据库操作失败
        """
        start_time = time.time()
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 执行更新
                    affected_rows = cursor.execute(sql, params)
                    
                    # 提交事务（如果启用自动提交）
                    if auto_commit:
                        conn.commit()
                        self.logger.info("事务已提交", step="transaction_commit")
                    
                    execution_time = time.time() - start_time
                    
                    # 确定操作类型
                    operation_type = sql.strip().split()[0].lower()
                    
                    # 记录操作日志
                    self.logger.log_database_operation(
                        operation=operation_type,
                        affected_rows=affected_rows,
                        query=sql
                    )
                    
                    self.logger.info(f"{operation_type.upper()}操作完成，影响 {affected_rows} 行，耗时 {execution_time:.3f}秒", 
                                   step="update_complete",
                                   extra_data={"affected_rows": affected_rows, "execution_time": execution_time})
                    
                    return affected_rows
                    
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"更新执行失败: {sql}", step="update_error", exception=e,
                            extra_data={"execution_time": execution_time})
            raise
    
    def execute_batch_update(self, sql: str, params_list: List[Tuple], auto_commit: bool = True) -> int:
        """
        批量执行更新SQL语句
        
        Args:
            sql: SQL更新语句
            params_list: SQL参数列表
            auto_commit: 是否自动提交事务
            
        Returns:
            总的受影响行数
            
        Raises:
            pymysql.Error: 数据库操作失败
        """
        start_time = time.time()
        total_affected = 0
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 批量执行
                    for params in params_list:
                        affected_rows = cursor.execute(sql, params)
                        total_affected += affected_rows
                    
                    # 提交事务（如果启用自动提交）
                    if auto_commit:
                        conn.commit()
                        self.logger.info("批量事务已提交", step="batch_transaction_commit")
                    
                    execution_time = time.time() - start_time
                    
                    # 确定操作类型
                    operation_type = sql.strip().split()[0].lower()
                    
                    # 记录操作日志
                    self.logger.log_database_operation(
                        operation=f"batch_{operation_type}",
                        affected_rows=total_affected,
                        query=f"{sql} (批量执行 {len(params_list)} 次)"
                    )
                    
                    self.logger.info(f"批量{operation_type.upper()}操作完成，总计影响 {total_affected} 行，耗时 {execution_time:.3f}秒", 
                                   step="batch_update_complete",
                                   extra_data={
                                       "batch_size": len(params_list),
                                       "total_affected": total_affected, 
                                       "execution_time": execution_time
                                   })
                    
                    return total_affected
                    
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"批量更新执行失败: {sql}", step="batch_update_error", exception=e,
                            extra_data={"batch_size": len(params_list), "execution_time": execution_time})
            raise
    
    @contextmanager
    def transaction(self):
        """
        事务上下文管理器
        
        Usage:
            with db_manager.transaction() as conn:
                cursor = conn.cursor()
                cursor.execute("INSERT INTO table1 ...")
                cursor.execute("UPDATE table2 ...")
                # 自动提交事务，如果发生异常则自动回滚
        
        Yields:
            数据库连接对象
        """
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            self.logger.info("开始事务", step="transaction_start")
            
            yield conn
            
            # 提交事务
            conn.commit()
            self.logger.info("事务提交成功", step="transaction_commit")
            
        except Exception as e:
            # 回滚事务
            if conn:
                conn.rollback()
                self.logger.warning("事务已回滚", step="transaction_rollback")
            
            self.logger.error(f"事务执行失败: {str(e)}", step="transaction_error", exception=e)
            raise
            
        finally:
            # 返回连接到池中
            if conn:
                self.connection_pool.return_connection(conn)
    
    def insert_and_get_id(self, sql: str, params: Tuple = None) -> int:
        """
        插入记录并返回自增ID
        
        Args:
            sql: INSERT SQL语句
            params: SQL参数元组
            
        Returns:
            新插入记录的自增ID
            
        Raises:
            pymysql.Error: 数据库操作失败
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 执行插入
                    cursor.execute(sql, params)
                    
                    # 获取自增ID
                    last_id = conn.insert_id()
                    
                    # 提交事务
                    conn.commit()
                    
                    # 记录操作日志
                    self.logger.log_database_operation(
                        operation="insert",
                        affected_rows=1,
                        query=sql
                    )
                    
                    self.logger.info(f"插入记录成功，新ID: {last_id}", step="insert_with_id_complete",
                                   extra_data={"last_insert_id": last_id})
                    
                    return last_id
                    
        except Exception as e:
            self.logger.error(f"插入记录失败: {sql}", step="insert_with_id_error", exception=e)
            raise
    
    def check_connection(self) -> bool:
        """
        检查数据库连接是否正常
        
        Returns:
            连接是否正常
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    
            self.logger.info("数据库连接检查正常", step="connection_check_ok")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接检查失败: {str(e)}", step="connection_check_error", exception=e)
            return False
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息
        
        Args:
            table_name: 表名
            
        Returns:
            表结构信息列表
        """
        sql = "DESCRIBE `{}`".format(table_name.replace('`', '``'))  # 防止SQL注入
        return self.execute_query(sql)
    
    def close_all_connections(self):
        """关闭所有数据库连接"""
        try:
            self.connection_pool.close_all()
            self.logger.info("所有数据库连接已关闭", step="close_all_connections")
        except Exception as e:
            self.logger.warning(f"关闭数据库连接时出现异常: {str(e)}", step="close_connections_warning")


def get_database_manager(business_type: str = None, script_name: str = None, task_id: str = None) -> DatabaseManager:
    """
    获取数据库管理器实例的便利函数
    
    Args:
        business_type: 业务类型
        script_name: 脚本名称
        task_id: 任务ID
        
    Returns:
        DatabaseManager实例
    """
    return DatabaseManager(business_type, script_name, task_id) 