"""
CSV导入工具 - 通用CSV文件导入到数据库

本工具直接复用export_publish_list模块中的CSV处理和数据库操作逻辑，
提供简单易用的API接口来实现CSV文件导入到数据库的功能。

使用示例:
    from app.template.csv_to_db_importer import CSVToDBImporter
    
    # 使用默认数据库配置
    importer = CSVToDBImporter()
    result = importer.import_csv_to_db('/path/to/your/file.csv')
    
    # 使用自定义数据库配置
    custom_db_config = {
        'host': 'your_host',
        'port': '3306',
        'username': 'your_user',
        'password': 'your_password',
        'database': 'your_database'
    }
    importer = CSVToDBImporter(database_config=custom_db_config)
    result = importer.import_csv_to_db('/path/to/your/file.csv')
    
    print(f"导入结果: {result['message']}")
    print(f"新增记录: {result['insert_count']}")
    print(f"更新记录: {result['update_count']}")
"""

import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from app.business.export_publish_list.csv_processor import CSVProcessor
from app.business.export_publish_list.db_operations import PublishSuccessDBOperator
from app.core.database import DatabaseManager, DatabaseConnectionPool
from app.utils.logger import get_rpa_logger


class CSVToDBImporter:
    """
    CSV导入工具类
    
    直接复用export_publish_list模块中的CSV处理和数据库操作逻辑，
    提供简单的API接口来实现CSV文件导入功能。
    """
    
    def __init__(self, business_type: str = "csv_template_import", 
                 script_name: str = "csv_to_db_importer", logger=None,
                 database_config: Dict[str, str] = None):
        """
        初始化CSV导入工具
        
        Args:
            business_type: 业务类型标识
            script_name: 脚本名称
            logger: 日志记录器，如果不提供则自动创建
            database_config: 自定义数据库配置，格式：
                {
                    'host': '数据库主机地址',
                    'port': '端口号',
                    'username': '用户名',
                    'password': '密码',
                    'database': '数据库名'
                }
        """
        self.business_type = business_type
        self.script_name = script_name
        self.database_config = database_config
        
        # 初始化日志记录器
        if logger:
            self.logger = logger
        else:
            self.logger = get_rpa_logger(business_type, script_name)
        
        # 记录配置信息（隐藏敏感信息）
        config_info = {}
        if database_config:
            config_info = {
                'custom_database': True,
                'host': database_config.get('host', 'N/A'),
                'database': database_config.get('database', 'N/A'),
                'port': database_config.get('port', 'N/A')
            }
        else:
            config_info = {'custom_database': False, 'using_default_config': True}
        
        self.logger.info("CSV导入工具初始化", extra_data={
            'business_type': business_type,
            'script_name': script_name,
            'class_name': self.__class__.__name__,
            **config_info
        })
        
        # 初始化CSV处理器（复用现有逻辑）
        self.csv_processor = CSVProcessor(logger=self.logger)
        
        # 初始化数据库操作器
        if database_config:
            # 使用自定义数据库配置
            self.db_operator = self._create_custom_db_operator(database_config)
        else:
            # 使用默认配置（复用现有逻辑）
            self.db_operator = PublishSuccessDBOperator(
                business_type=business_type,
                script_name=script_name,
                logger=self.logger
            )
        
        self.logger.info("CSV导入工具初始化完成")
    
    def import_csv_to_db(self, csv_file_path: str, force_update: bool = False) -> Dict[str, Any]:
        """
        将CSV文件导入到数据库
        
        Args:
            csv_file_path: CSV文件路径
            force_update: 是否强制更新已存在的记录（不检查变化）
            
        Returns:
            Dict[str, Any]: 导入结果
                {
                    'success': bool,           # 是否成功
                    'message': str,           # 结果消息
                    'total_records': int,     # 总记录数
                    'insert_count': int,      # 新增记录数
                    'update_count': int,      # 更新记录数
                    'skip_count': int,        # 跳过记录数
                    'error_count': int,       # 错误记录数
                    'file_info': dict,        # 文件信息
                    'process_time': float     # 处理时间（秒）
                }
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始导入CSV文件", extra_data={
                'csv_file_path': csv_file_path,
                'force_update': force_update
            })
            
            # 验证文件是否存在
            if not os.path.exists(csv_file_path):
                error_msg = f"CSV文件不存在: {csv_file_path}"
                self.logger.error(error_msg)
                return {
                    'success': False,
                    'message': error_msg,
                    'total_records': 0,
                    'insert_count': 0,
                    'update_count': 0,
                    'skip_count': 0,
                    'error_count': 0,
                    'file_info': {},
                    'process_time': 0
                }
            
            # 获取文件信息
            file_stat = os.stat(csv_file_path)
            file_info = {
                'file_path': csv_file_path,
                'file_name': os.path.basename(csv_file_path),
                'file_size': file_stat.st_size,
                'file_size_mb': round(file_stat.st_size / (1024 * 1024), 2),
                'last_modified': datetime.fromtimestamp(file_stat.st_mtime).isoformat()
            }
            
            self.logger.info(f"文件信息", extra_data=file_info)
            
            # 读取和解析CSV文件（复用现有逻辑）
            self.logger.info("开始读取CSV文件内容")
            with open(csv_file_path, 'rb') as file:
                content = file.read()
            
            # 使用现有的CSV处理器解析内容
            records = self.csv_processor.parse_csv_content(content)
            
            if not records:
                warning_msg = "CSV文件解析后没有有效记录"
                self.logger.warning(warning_msg, extra_data=file_info)
                return {
                    'success': True,
                    'message': warning_msg,
                    'total_records': 0,
                    'insert_count': 0,
                    'update_count': 0,
                    'skip_count': 0,
                    'error_count': 0,
                    'file_info': file_info,
                    'process_time': (datetime.now() - start_time).total_seconds()
                }
            
            # 使用现有的数据库操作器处理记录
            self.logger.info(f"开始处理 {len(records)} 条记录")
            db_result = self.db_operator.process_records(records, force_update=force_update)
            
            # 计算处理时间
            process_time = (datetime.now() - start_time).total_seconds()
            
            # 组装最终结果
            final_result = {
                'success': db_result['success'],
                'message': db_result['message'],
                'total_records': db_result['total_records'],
                'insert_count': db_result['insert_count'],
                'update_count': db_result['update_count'],
                'skip_count': db_result.get('skip_count', 0),
                'error_count': db_result['error_count'],
                'file_info': file_info,
                'process_time': round(process_time, 2)
            }
            
            # 记录最终结果
            self.logger.info(f"CSV导入完成", extra_data=final_result)
            
            return final_result
            
        except Exception as e:
            process_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"CSV导入失败: {str(e)}"
            
            self.logger.error(error_msg, extra_data={
                'csv_file_path': csv_file_path,
                'exception_type': type(e).__name__,
                'process_time': process_time
            })
            
            return {
                'success': False,
                'message': error_msg,
                'total_records': 0,
                'insert_count': 0,
                'update_count': 0,
                'skip_count': 0,
                'error_count': 0,
                'file_info': {},
                'process_time': round(process_time, 2)
            }
    
    def get_database_statistics(self) -> Dict[str, Any]:
        """
        获取数据库表统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            return self.db_operator.get_table_statistics()
        except Exception as e:
            self.logger.error(f"获取数据库统计信息失败: {str(e)}")
            return {
                'error': str(e),
                'total_count': 0,
                'today_count': 0,
                'platform_stats': []
            }
    
    def validate_csv_file(self, csv_file_path: str) -> Dict[str, Any]:
        """
        验证CSV文件格式和内容（不导入到数据库）
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            self.logger.info(f"开始验证CSV文件", extra_data={
                'csv_file_path': csv_file_path
            })
            
            # 验证文件是否存在
            if not os.path.exists(csv_file_path):
                return {
                    'valid': False,
                    'message': f"文件不存在: {csv_file_path}",
                    'records_count': 0,
                    'sample_records': []
                }
            
            # 读取文件内容
            with open(csv_file_path, 'rb') as file:
                content = file.read()
            
            # 解析CSV内容
            records = self.csv_processor.parse_csv_content(content)
            
            # 取前5条记录作为样例
            sample_records = records[:5] if records else []
            
            result = {
                'valid': len(records) > 0,
                'message': f"文件验证成功，包含{len(records)}条有效记录" if records else "文件验证失败，没有有效记录",
                'records_count': len(records),
                'sample_records': sample_records
            }
            
            self.logger.info(f"CSV文件验证完成", extra_data=result)
            
            return result
            
        except Exception as e:
            error_msg = f"CSV文件验证失败: {str(e)}"
            self.logger.error(error_msg, extra_data={
                'csv_file_path': csv_file_path,
                'exception_type': type(e).__name__
            })
            
            return {
                'valid': False,
                'message': error_msg,
                'records_count': 0,
                'sample_records': []
            }
    
    def _create_custom_db_operator(self, database_config: Dict[str, str]) -> 'CustomPublishSuccessDBOperator':
        """
        创建自定义数据库操作器
        
        Args:
            database_config: 自定义数据库配置
            
        Returns:
            CustomPublishSuccessDBOperator: 自定义数据库操作器实例
        """
        try:
            self.logger.info("创建自定义数据库操作器", extra_data={
                'host': database_config.get('host'),
                'database': database_config.get('database'),
                'port': database_config.get('port')
            })
            
            return CustomPublishSuccessDBOperator(
                database_config=database_config,
                business_type=self.business_type,
                script_name=self.script_name,
                logger=self.logger
            )
            
        except Exception as e:
            self.logger.error(f"创建自定义数据库操作器失败: {str(e)}")
            raise


class CustomPublishSuccessDBOperator(PublishSuccessDBOperator):
    """自定义数据库配置的发布成功列表数据库操作器"""
    
    def __init__(self, database_config: Dict[str, str], business_type: str = "export_publish_list", 
                 script_name: str = "csv_import", logger: logging.Logger = None):
        """
        初始化自定义数据库操作器
        
        Args:
            database_config: 自定义数据库配置
            business_type: 业务类型
            script_name: 脚本名称
            logger: 日志记录器
        """
        self.business_type = business_type
        self.script_name = script_name
        self.logger = logger or logging.getLogger(__name__)
        
        # 验证数据库配置
        required_keys = ['host', 'port', 'username', 'password', 'database']
        missing_keys = [key for key in required_keys if key not in database_config]
        if missing_keys:
            raise ValueError(f"数据库配置缺少必要字段: {missing_keys}")
        
        # 创建自定义的数据库管理器
        self.db_manager = self._create_custom_database_manager(database_config)
        
        # 表名和字段配置（从父类继承）
        self.table_name = "yimai_publish_success_list"
        
        # 字段列表（排除auto_increment的id字段）
        self.insert_fields = [
            'platform', 'source', 'sku', 'spu', 'seller_sku_parent', 'seller_sku_child',
            'asin', 'upc', 'ean', 'brand', 'part_number', 'variant_type',
            'variant_name_1', 'variant_name_2', 'variant_name_3', 'price_local_currency',
            'quantity', 'account', 'site', 'warehouse', 'node_id', 'platform_category_path',
            'product_line', 'product_type', 'chinese_title', 'sales_person',
            'platform_return_message', 'link_online_status', 'creator', 'create_time',
            'last_fetch_time', 'sync_time'
        ]
        
        # 更新字段（排除唯一索引字段和ID）
        self.update_fields = [field for field in self.insert_fields 
                             if field not in ['seller_sku_child', 'account']]
        
        self.logger.info("自定义数据库操作器初始化完成", extra_data={
            'table_name': self.table_name,
            'insert_fields_count': len(self.insert_fields),
            'update_fields_count': len(self.update_fields),
            'custom_database': f"{database_config['host']}:{database_config['port']}/{database_config['database']}"
        })
    
    def _create_custom_database_manager(self, database_config: Dict[str, str]) -> 'CustomDatabaseManager':
        """
        创建自定义数据库管理器
        
        Args:
            database_config: 数据库配置
            
        Returns:
            CustomDatabaseManager: 自定义数据库管理器
        """
        return CustomDatabaseManager(
            database_config=database_config,
            business_type=self.business_type,
            script_name=self.script_name,
            logger=self.logger
        )


class CustomDatabaseManager(DatabaseManager):
    """自定义配置的数据库管理器"""
    
    def __init__(self, database_config: Dict[str, str], business_type: str = None, 
                 script_name: str = None, logger=None):
        """
        初始化自定义数据库管理器
        
        Args:
            database_config: 自定义数据库配置
            business_type: 业务类型
            script_name: 脚本名称
            logger: 日志记录器
        """
        self.business_type = business_type
        self.script_name = script_name
        
        # 使用自定义配置覆盖默认配置
        self.config = database_config
        
        # 初始化日志记录器
        if logger:
            self.logger = logger
        else:
            from app.utils.logger import get_rpa_logger
            self.logger = get_rpa_logger(business_type or 'database', script_name or 'db')
        
        # 生成连接池键（基于配置内容）
        self.pool_key = f"{self.config['host']}:{self.config['port']}/{self.config['database']}_custom"
        
        # 获取或创建连接池
        self._ensure_connection_pool()
        
        self.logger.info("自定义数据库管理器初始化完成", extra_data={
            "host": self.config['host'],
            "database": self.config['database'],
            "pool_key": self.pool_key
        })
    
    def _ensure_connection_pool(self):
        """确保连接池存在"""
        from threading import Lock
        if not hasattr(self.__class__, '_custom_connection_pools'):
            self.__class__._custom_connection_pools = {}
            self.__class__._custom_pool_lock = Lock()
            
        with self.__class__._custom_pool_lock:
            if self.pool_key not in self.__class__._custom_connection_pools:
                # 创建新的连接池
                pool_size = 10  # 默认连接池大小
                max_overflow = 20  # 默认最大溢出
                
                self.__class__._custom_connection_pools[self.pool_key] = DatabaseConnectionPool(
                    self.config, pool_size, max_overflow
                )
                self.logger.info(f"创建自定义数据库连接池: {self.pool_key}")
    
    @property 
    def connection_pool(self) -> DatabaseConnectionPool:
        """获取当前的连接池"""
        return self.__class__._custom_connection_pools[self.pool_key] 