# RPA-K8s 完整依赖文件
# 基于代码分析结果，包含所有业务代码实际使用的依赖包

# ========================================
# 核心Web自动化框架
# ========================================
playwright==1.49.0
selenium==4.27.1

# ========================================
# 网络请求和异步处理
# ========================================
aiohttp==3.12.13
requests==2.32.3
aiofiles==24.1.0

# ========================================
# 数据处理和Excel操作
# ========================================
pandas==2.2.3
openpyxl==3.1.5
xlsxwriter==3.2.0

# ========================================
# 数据库连接
# ========================================
pymysql==1.1.1
aiomysql==0.2.0
cryptography>=3.4.8  # MySQL 8.0 认证支持

# ========================================
# HTML解析和文本处理
# ========================================
beautifulsoup4==4.12.3

# ========================================
# 配置和环境管理
# ========================================
python-dotenv==1.0.1
pytz==2024.2

# ========================================
# 可能需要的额外依赖
# ========================================
# 字符编码检测
chardet==5.2.0

# 日期时间处理
python-dateutil==2.9.0 