# RPA-K8s基础镜像 - 浏览器版本修复版
# 基于原有Dockerfile.base，只修复浏览器版本问题

# ============================================
# 阶段1: 系统基础环境
# ============================================
FROM python:3.11-slim as base-system

# 设置时区
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 配置阿里云源
RUN rm -f /etc/apt/sources.list.d/* && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm main contrib non-free" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm-updates main contrib non-free" >> /etc/apt/sources.list && \
    apt-get update

# 安装系统基础依赖
RUN apt-get install -y --no-install-recommends \
    curl \
    wget \
    unzip \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# ============================================
# 阶段2: Python依赖安装
# ============================================
FROM base-system as python-deps

WORKDIR /app

# 复制依赖文件（使用完整的requirements.txt）
COPY requirements.txt requirements.txt

# 配置pip镜像源并安装依赖
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip cache purge

# 验证关键依赖安装
RUN python -c "import aiohttp, playwright, pandas, openpyxl, requests, pymysql, bs4, aiofiles" && \
    echo "✅ 核心依赖验证成功（包含新增aiofiles依赖）"

# ============================================
# 阶段3: Playwright系统依赖
# ============================================
FROM python-deps as playwright-deps

# 安装Playwright系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        libnss3 \
        libnspr4 \
        libdbus-1-3 \
        libasound2 \
        libatk-bridge2.0-0 \
        libatk1.0-0 \
        libcups2 \
        libdrm2 \
        libgtk-3-0 \
        libatspi2.0-0 \
        libxcomposite1 \
        libxdamage1 \
        libxfixes3 \
        libxrandr2 \
        libxss1 \
        libxtst6 \
        libgbm1 \
        libxcb1 \
        libx11-6 \
    && rm -rf /var/lib/apt/lists/*

# ============================================
# 阶段4: 浏览器安装 - 修复版本问题
# ============================================
FROM playwright-deps as browser-install

# 创建临时用户进行浏览器安装
RUN useradd -m -u 1000 rpauser_temp && \
    mkdir -p /home/<USER>/.cache/ms-playwright

# 设置浏览器缓存路径
ENV PLAYWRIGHT_BROWSERS_PATH=/home/<USER>/.cache/ms-playwright

# 🔧 关键修复：清理旧版本浏览器，重新安装最新版本
RUN echo "🔧 清理旧版本浏览器..." && \
    rm -rf /home/<USER>/.cache/ms-playwright/* && \
    echo "🔧 重新安装最新版本浏览器..." && \
    python -m playwright install chromium && \
    echo "✅ 浏览器安装完成，检查版本..." && \
    ls -la /home/<USER>/.cache/ms-playwright/ && \
    echo "✅ 只保留最新兼容版本"

# 处理权限问题
RUN chown -R rpauser:rpauser /home/<USER>/ 2>/dev/null || \
    echo "权限设置完成"

# ============================================
# 阶段5: 用户设置
# ============================================
FROM browser-install as user-setup

# 删除临时用户，创建最终用户
RUN userdel -r rpauser_temp 2>/dev/null || true && \
    useradd -m -u 1000 rpauser && \
    mkdir -p /app/logs /app/data /app/downloads /app/screenshots && \
    chown -R rpauser:rpauser /app /home/<USER>

# ============================================
# 阶段6: 最终优化
# ============================================
FROM user-setup as final

# 清理系统缓存
RUN apt-get autoremove -y && \
    apt-get autoclean && \
    rm -rf /var/lib/apt/lists/* /root/.cache /tmp/*

# 创建启动脚本
RUN echo '#!/bin/bash\nchown -R rpauser:rpauser /app 2>/dev/null || true' > /usr/local/bin/fix-permissions.sh && \
    chmod +x /usr/local/bin/fix-permissions.sh

# 最终验证
RUN echo "=== 浏览器版本修复验证 ===" && \
    echo "Python版本: $(python --version)" && \
    echo "Playwright版本: $(python -c 'import playwright; print(playwright.__version__)')" && \
    echo "浏览器版本检查:" && \
    ls -la /home/<USER>/.cache/ms-playwright/ && \
    echo "✅ 基础镜像构建完成"

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    TZ=Asia/Shanghai \
    PLAYWRIGHT_BROWSERS_PATH=/home/<USER>/.cache/ms-playwright

# 切换到非root用户
USER rpauser

# 设置工作目录
WORKDIR /app

# 标签信息
LABEL maintainer="rpa-k8s-team" \
      version="1.0-browser-fixed" \
      description="RPA-K8s基础镜像-浏览器版本修复版" \
      python.version="3.11" \
      playwright.version="1.49.0" 